<?php

namespace Sparefoot\MyFootService\EventSubscriber;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ControllerEvent;
use Symfony\Component\HttpKernel\KernelEvents;

class ControllerInitSubscriber implements EventSubscriberInterface
{
    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::CONTROLLER => 'onKernelController',
        ];
    }

    public function onKernelController(ControllerEvent $event): void
    {
        $controller = $event->getController();

        // Handle both array callables and single object callables
        if (is_array($controller)) {
            $controllerObject = $controller[0];
        } elseif (is_object($controller) && method_exists($controller, '__invoke')) {
            $controllerObject = $controller;
        } else {
            return;
        }

        $request = $event->getRequest();

        // Call the initBeforeControllerAction method
        if (method_exists($controllerObject, 'initBeforeControllerAction')) {
            $controllerObject->initBeforeControllerAction($request);
        }

        // Call the initializeRestrictedController method
        if (method_exists($controllerObject, 'initializeRestrictedController')) {
            $controllerObject->initBeforeControllerAction($request);
        }
    }
}
